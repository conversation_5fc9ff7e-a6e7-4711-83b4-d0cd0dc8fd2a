<script setup lang="ts">
import type { MenuRecordRaw } from '@vben-core/typings';

import type { MenuProps } from './types';

import { computed } from 'vue';
import { useForwardProps } from '@vben-core/composables';

import { Menu } from './components';
import SubMenu from './sub-menu.vue';

interface Props extends MenuProps {
  menus: MenuRecordRaw[];
}

defineOptions({
  name: 'MenuView',
});

const props = withDefaults(defineProps<Props>(), {
  collapse: false,
  // theme: 'dark',
});

const forward = useForwardProps(props);

// 过滤掉隐藏的菜单项
const visibleMenus = computed(() => {
  return props.menus.filter(menu => menu.show !== false);
});

// const emit = defineEmits<{
//   'update:openKeys': [key: Key[]];
//   'update:selectedKeys': [key: Key[]];
// }>();
</script>

<template>
  <Menu v-bind="forward">
    <template v-for="menu in visibleMenus" :key="menu.path">
      <SubMenu :menu="menu" />
    </template>
  </Menu>
</template>
