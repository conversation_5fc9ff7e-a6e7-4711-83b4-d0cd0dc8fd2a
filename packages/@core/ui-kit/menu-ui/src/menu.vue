<script setup lang="ts">
import type { MenuRecordRaw } from '@vben-core/typings';

import type { MenuProps } from './types';

import { computed } from 'vue';
import { useForwardProps } from '@vben-core/composables';

import { Menu } from './components';
import SubMenu from './sub-menu.vue';

interface Props extends MenuProps {
  menus: MenuRecordRaw[];
}

defineOptions({
  name: 'MenuView',
});

const props = withDefaults(defineProps<Props>(), {
  collapse: false,
  // theme: 'dark',
});

const forward = useForwardProps(props);

// 递归过滤隐藏的菜单项
const filterMenus = (menus: any[], level = 0): any[] => {
  return menus.filter(menu => {
    const indent = '  '.repeat(level);
    console.log(`🔍 Menu组件 ${indent}处理菜单项:`, {
      name: menu.name,
      show: menu.show,
      path: menu.path,
      level
    });

    // 调试凭证审核菜单
    if (menu.name === '凭证审核') {
      console.log('🔍 Menu组件 发现凭证审核菜单:', {
        name: menu.name,
        show: menu.show,
        path: menu.path,
        menu: menu
      });
    }

    // 检查 show 属性
    if (menu.show === false) {
      console.log(`🔍 Menu组件 ${indent}隐藏菜单:`, menu.name);
      return false;
    }

    // 递归处理子菜单
    if (menu.children && menu.children.length > 0) {
      console.log(`🔍 Menu组件 ${indent}处理子菜单:`, menu.name, '子菜单数量:', menu.children.length);
      menu.children = filterMenus(menu.children, level + 1);
    }

    return true;
  });
};

// 过滤掉隐藏的菜单项
const visibleMenus = computed(() => {
  console.log('🔍 Menu组件 原始菜单数据:', props.menus);
  const filtered = filterMenus(props.menus);
  console.log('🔍 Menu组件 过滤后菜单数据:', filtered);
  return filtered;
});

// const emit = defineEmits<{
//   'update:openKeys': [key: Key[]];
//   'update:selectedKeys': [key: Key[]];
// }>();
</script>

<template>
  <Menu v-bind="forward">
    <template v-for="menu in visibleMenus" :key="menu.path">
      <SubMenu :menu="menu" />
    </template>
  </Menu>
</template>
